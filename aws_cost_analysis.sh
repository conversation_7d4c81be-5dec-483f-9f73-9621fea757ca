#!/bin/bash

# AWS Cost Analysis Script
# This script analyzes AWS costs and generates insights

# Set the account to analyze
ACCOUNT_ID="YOUR-MEMBER-ACCOUNT-ID"
# Set time period (last 3 months by default)
START_DATE=$(date -d "3 months ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)

echo "Analyzing AWS costs from $START_DATE to $END_DATE for account $ACCOUNT_ID"

# Get cost by service
echo "=== Cost by Service ==="
aws ce get-cost-and-usage \
  --time-period Start=$START_DATE,End=$END_DATE \
  --granularity MONTHLY \
  --metrics "UnblendedCost" \
  --group-by Type=DIMENSION,Key=SERVICE \
  --filter "{\"Dimensions\": {\"Key\": \"LINKED_ACCOUNT\", \"Values\": [\"$ACCOUNT_ID\"]}}" \
  --output table

# Get cost by region
echo "=== Cost by Region ==="
aws ce get-cost-and-usage \
  --time-period Start=$START_DATE,End=$END_DATE \
  --granularity MONTHLY \
  --metrics "UnblendedCost" \
  --group-by Type=DIMENSION,Key=REGION \
  --filter "{\"Dimensions\": {\"Key\": \"LINKED_ACCOUNT\", \"Values\": [\"$ACCOUNT_ID\"]}}" \
  --output table

# Get cost by tag (if you use tags for cost allocation)
echo "=== Cost by Project Tag ==="
aws ce get-cost-and-usage \
  --time-period Start=$START_DATE,End=$END_DATE \
  --granularity MONTHLY \
  --metrics "UnblendedCost" \
  --group-by Type=TAG,Key=Project \
  --filter "{\"Dimensions\": {\"Key\": \"LINKED_ACCOUNT\", \"Values\": [\"$ACCOUNT_ID\"]}}" \
  --output table

# Get EC2 instance recommendations
echo "=== EC2 Rightsizing Recommendations ==="
aws ce get-rightsizing-recommendation \
  --service "AmazonEC2" \
  --filter "{\"Dimensions\": {\"Key\": \"LINKED_ACCOUNT\", \"Values\": [\"$ACCOUNT_ID\"]}}" \
  --output table